{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/newwww/apps/frontend/src/components/sections/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useTranslations, useLocale } from 'next-intl';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport useSWR from 'swr';\nimport { ArrowRightIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';\n\nexport function HeroSection() {\n  const t = useTranslations('home');\n  const locale = useLocale();\n  const isRTL = locale === 'ar';\n  const { data: settings } = useSWR('/api/settings');\n\n  const politicianData = settings?.data?.settings;\n\n  return (\n    <section className=\"relative bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 text-white overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 bg-black/20\" />\n      <div className=\"absolute inset-0 bg-[url('/images/pattern.svg')] opacity-10\" />\n      \n      <div className=\"relative mx-auto max-w-7xl px-4 py-24 sm:px-6 lg:px-8 lg:py-32\">\n        <div className=\"grid grid-cols-1 gap-12 lg:grid-cols-2 lg:gap-16 items-center\">\n          {/* Content */}\n          <div className={`${isRTL ? 'lg:order-2' : 'lg:order-1'} space-y-8`}>\n            <div className=\"space-y-4\">\n              <h1 className=\"text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl\">\n                {politicianData?.politicianName?.[locale] || t('heroTitle')}\n              </h1>\n              <p className=\"text-xl text-blue-100 sm:text-2xl\">\n                {politicianData?.politicianTitle?.[locale] || t('heroSubtitle')}\n              </p>\n            </div>\n            \n            <p className=\"text-lg text-blue-100 leading-relaxed\">\n              {politicianData?.welcomeMessage?.[locale] || t('heroSubtitle')}\n            </p>\n            \n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <Link\n                href={`/${locale}/biography`}\n                className=\"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-blue-900 bg-white hover:bg-blue-50 transition-colors duration-200\"\n              >\n                {t('biography')}\n                {isRTL ? (\n                  <ArrowLeftIcon className=\"mr-2 h-5 w-5\" />\n                ) : (\n                  <ArrowRightIcon className=\"ml-2 h-5 w-5\" />\n                )}\n              </Link>\n              \n              <Link\n                href={`/${locale}/contact`}\n                className=\"inline-flex items-center justify-center px-6 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-blue-900 transition-colors duration-200\"\n              >\n                {t('contact')}\n              </Link>\n            </div>\n          </div>\n          \n          {/* Image */}\n          <div className={`${isRTL ? 'lg:order-1' : 'lg:order-2'} relative`}>\n            <div className=\"relative mx-auto w-80 h-80 lg:w-96 lg:h-96\">\n              {/* Decorative circles */}\n              <div className=\"absolute -top-4 -right-4 w-72 h-72 bg-blue-400/20 rounded-full blur-3xl\" />\n              <div className=\"absolute -bottom-4 -left-4 w-72 h-72 bg-blue-300/20 rounded-full blur-3xl\" />\n              \n              {/* Main image container */}\n              <div className=\"relative w-full h-full rounded-full overflow-hidden border-4 border-white/20 shadow-2xl\">\n                <Image\n                  src={politicianData?.politicianImage || '/images/politician-placeholder.jpg'}\n                  alt={politicianData?.politicianName?.[locale] || 'Iraqi Politician'}\n                  fill\n                  className=\"object-cover\"\n                  priority\n                  sizes=\"(max-width: 768px) 320px, 384px\"\n                />\n              </div>\n              \n              {/* Floating badge */}\n              <div className=\"absolute -bottom-2 left-1/2 transform -translate-x-1/2 bg-white text-blue-900 px-4 py-2 rounded-full shadow-lg\">\n                <span className=\"text-sm font-semibold\">\n                  {locale === 'ar' ? 'خدمة الشعب' : 'Serving the People'}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      {/* Bottom wave */}\n      <div className=\"absolute bottom-0 left-0 right-0\">\n        <svg\n          className=\"w-full h-12 text-white\"\n          viewBox=\"0 0 1200 120\"\n          preserveAspectRatio=\"none\"\n        >\n          <path\n            d=\"M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z\"\n            opacity=\".25\"\n            fill=\"currentColor\"\n          />\n          <path\n            d=\"M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z\"\n            opacity=\".5\"\n            fill=\"currentColor\"\n          />\n          <path\n            d=\"M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z\"\n            fill=\"currentColor\"\n          />\n        </svg>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AACA;AAAA;;;AANA;;;;;;AAQO,SAAS;;IACd,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;IACvB,MAAM,QAAQ,WAAW;IACzB,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAM,AAAD,EAAE;IAElC,MAAM,iBAAiB,UAAU,MAAM;IAEvC,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAW,GAAG,QAAQ,eAAe,aAAa,UAAU,CAAC;;8CAChE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,gBAAgB,gBAAgB,CAAC,OAAO,IAAI,EAAE;;;;;;sDAEjD,6LAAC;4CAAE,WAAU;sDACV,gBAAgB,iBAAiB,CAAC,OAAO,IAAI,EAAE;;;;;;;;;;;;8CAIpD,6LAAC;oCAAE,WAAU;8CACV,gBAAgB,gBAAgB,CAAC,OAAO,IAAI,EAAE;;;;;;8CAGjD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,CAAC,EAAE,OAAO,UAAU,CAAC;4CAC5B,WAAU;;gDAET,EAAE;gDACF,sBACC,6LAAC,4NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;yEAEzB,6LAAC,8NAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;;;;;;;sDAI9B,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,CAAC,EAAE,OAAO,QAAQ,CAAC;4CAC1B,WAAU;sDAET,EAAE;;;;;;;;;;;;;;;;;;sCAMT,6LAAC;4BAAI,WAAW,GAAG,QAAQ,eAAe,aAAa,SAAS,CAAC;sCAC/D,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDAGf,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,gBAAgB,mBAAmB;4CACxC,KAAK,gBAAgB,gBAAgB,CAAC,OAAO,IAAI;4CACjD,IAAI;4CACJ,WAAU;4CACV,QAAQ;4CACR,OAAM;;;;;;;;;;;kDAKV,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDACb,WAAW,OAAO,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,SAAQ;oBACR,qBAAoB;;sCAEpB,6LAAC;4BACC,GAAE;4BACF,SAAQ;4BACR,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,SAAQ;4BACR,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;;;;;;;;;;;;;;;;;;AAMjB;GA5GgB;;QACJ,yMAAA,CAAA,kBAAe;QACV,qKAAA,CAAA,YAAS;QAEG,iKAAA,CAAA,UAAM;;;KAJnB", "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/newwww/apps/frontend/src/components/sections/WelcomeSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useTranslations, useLocale } from 'next-intl';\nimport useSWR from 'swr';\n\nexport function WelcomeSection() {\n  const t = useTranslations('home');\n  const locale = useLocale();\n  const { data: settings } = useSWR('/api/settings');\n\n  const welcomeMessage = settings?.data?.settings?.welcomeMessage?.[locale] || t('welcomeMessage');\n\n  return (\n    <section className=\"py-16 bg-gray-50 dark:bg-gray-800\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"mx-auto max-w-3xl text-center\">\n          <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl\">\n            {t('welcomeMessage')}\n          </h2>\n          <div className=\"mt-6 text-lg leading-8 text-gray-600 dark:text-gray-300\">\n            <p className=\"whitespace-pre-line\">\n              {welcomeMessage}\n            </p>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAM,AAAD,EAAE;IAElC,MAAM,iBAAiB,UAAU,MAAM,UAAU,gBAAgB,CAAC,OAAO,IAAI,EAAE;IAE/E,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACX,EAAE;;;;;;kCAEL,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAvBgB;;QACJ,yMAAA,CAAA,kBAAe;QACV,qKAAA,CAAA,YAAS;QACG,iKAAA,CAAA,UAAM;;;KAHnB", "debugId": null}}, {"offset": {"line": 368, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/newwww/apps/frontend/src/components/sections/LatestNews.tsx"], "sourcesContent": ["'use client';\n\nimport { useTranslations, useLocale } from 'next-intl';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport useSWR from 'swr';\nimport { format } from 'date-fns';\nimport { ar, enUS } from 'date-fns/locale';\nimport { ArrowRightIcon, ArrowLeftIcon, ClockIcon } from '@heroicons/react/24/outline';\n\nexport function LatestNews() {\n  const t = useTranslations();\n  const locale = useLocale();\n  const isRTL = locale === 'ar';\n  \n  const { data: newsData, error } = useSWR('/api/news?limit=3&status=published');\n  \n  if (error) {\n    return null;\n  }\n\n  const news = newsData?.data?.news || [];\n\n  if (news.length === 0) {\n    return null;\n  }\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return format(date, 'PPP', { \n      locale: locale === 'ar' ? ar : enUS \n    });\n  };\n\n  return (\n    <section className=\"py-16 bg-white dark:bg-gray-900\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"flex items-center justify-between mb-12\">\n          <div>\n            <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl\">\n              {t('home.latestNews')}\n            </h2>\n            <p className=\"mt-2 text-lg text-gray-600 dark:text-gray-300\">\n              {locale === 'ar' \n                ? 'تابع آخر الأخبار والتطورات السياسية'\n                : 'Follow the latest news and political developments'\n              }\n            </p>\n          </div>\n          \n          <Link\n            href={`/${locale}/news`}\n            className=\"inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium transition-colors duration-200\"\n          >\n            {t('home.viewAllNews')}\n            {isRTL ? (\n              <ArrowLeftIcon className=\"mr-2 h-5 w-5\" />\n            ) : (\n              <ArrowRightIcon className=\"ml-2 h-5 w-5\" />\n            )}\n          </Link>\n        </div>\n\n        {/* News Grid */}\n        <div className=\"grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3\">\n          {news.map((article: any, index: number) => (\n            <article\n              key={article._id}\n              className=\"group relative bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden\"\n            >\n              {/* Breaking News Badge */}\n              {article.isBreaking && (\n                <div className=\"absolute top-4 left-4 z-10 bg-red-600 text-white px-2 py-1 rounded text-xs font-semibold\">\n                  {t('news.breakingNews')}\n                </div>\n              )}\n\n              {/* Featured Image */}\n              <div className=\"relative h-48 bg-gray-200 dark:bg-gray-700\">\n                {article.featuredImage ? (\n                  <Image\n                    src={article.featuredImage}\n                    alt={article.title[locale]}\n                    fill\n                    className=\"object-cover group-hover:scale-105 transition-transform duration-200\"\n                    sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n                  />\n                ) : (\n                  <div className=\"flex items-center justify-center h-full\">\n                    <div className=\"text-gray-400 dark:text-gray-500\">\n                      <svg className=\"h-12 w-12\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path d=\"M4 4h16v12H4V4zm2 2v8h12V6H6zm2 2h8v4H8V8z\"/>\n                      </svg>\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              {/* Content */}\n              <div className=\"p-6\">\n                {/* Meta */}\n                <div className=\"flex items-center text-sm text-gray-500 dark:text-gray-400 mb-3\">\n                  <ClockIcon className=\"h-4 w-4 mr-1 rtl:mr-0 rtl:ml-1\" />\n                  <time dateTime={article.publishedAt}>\n                    {formatDate(article.publishedAt)}\n                  </time>\n                  {article.views > 0 && (\n                    <>\n                      <span className=\"mx-2\">•</span>\n                      <span>{article.views} {t('news.views')}</span>\n                    </>\n                  )}\n                </div>\n\n                {/* Title */}\n                <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-3 line-clamp-2\">\n                  <Link\n                    href={`/${locale}/news/${article.slug}`}\n                    className=\"hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200\"\n                  >\n                    {article.title[locale]}\n                  </Link>\n                </h3>\n\n                {/* Excerpt */}\n                <p className=\"text-gray-600 dark:text-gray-300 mb-4 line-clamp-3\">\n                  {article.excerpt[locale]}\n                </p>\n\n                {/* Tags */}\n                {article.tags && article.tags.length > 0 && (\n                  <div className=\"flex flex-wrap gap-2 mb-4\">\n                    {article.tags.slice(0, 3).map((tag: string) => (\n                      <span\n                        key={tag}\n                        className=\"inline-block bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded text-xs\"\n                      >\n                        #{tag}\n                      </span>\n                    ))}\n                  </div>\n                )}\n\n                {/* Read More */}\n                <Link\n                  href={`/${locale}/news/${article.slug}`}\n                  className=\"inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium text-sm transition-colors duration-200\"\n                >\n                  {t('common.readMore')}\n                  {isRTL ? (\n                    <ArrowLeftIcon className=\"mr-1 h-4 w-4\" />\n                  ) : (\n                    <ArrowRightIcon className=\"ml-1 h-4 w-4\" />\n                  )}\n                </Link>\n              </div>\n            </article>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;;;AARA;;;;;;;;AAUO,SAAS;;IACd,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;IACvB,MAAM,QAAQ,WAAW;IAEzB,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAM,AAAD,EAAE;IAEzC,IAAI,OAAO;QACT,OAAO;IACT;IAEA,MAAM,OAAO,UAAU,MAAM,QAAQ,EAAE;IAEvC,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB,OAAO;IACT;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,OAAO;YACzB,QAAQ,WAAW,OAAO,8IAAA,CAAA,KAAE,GAAG,oJAAA,CAAA,OAAI;QACrC;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAEL,6LAAC;oCAAE,WAAU;8CACV,WAAW,OACR,wCACA;;;;;;;;;;;;sCAKR,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAM,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC;4BACvB,WAAU;;gCAET,EAAE;gCACF,sBACC,6LAAC,4NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;yDAEzB,6LAAC,8NAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;;8BAMhC,6LAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,SAAc,sBACvB,6LAAC;4BAEC,WAAU;;gCAGT,QAAQ,UAAU,kBACjB,6LAAC;oCAAI,WAAU;8CACZ,EAAE;;;;;;8CAKP,6LAAC;oCAAI,WAAU;8CACZ,QAAQ,aAAa,iBACpB,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAK,QAAQ,aAAa;wCAC1B,KAAK,QAAQ,KAAK,CAAC,OAAO;wCAC1B,IAAI;wCACJ,WAAU;wCACV,OAAM;;;;;6DAGR,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAY,MAAK;gDAAe,SAAQ;0DACrD,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQlB,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,6LAAC;oDAAK,UAAU,QAAQ,WAAW;8DAChC,WAAW,QAAQ,WAAW;;;;;;gDAEhC,QAAQ,KAAK,GAAG,mBACf;;sEACE,6LAAC;4DAAK,WAAU;sEAAO;;;;;;sEACvB,6LAAC;;gEAAM,QAAQ,KAAK;gEAAC;gEAAE,EAAE;;;;;;;;;;;;;;;sDAM/B,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,CAAC,EAAE,OAAO,MAAM,EAAE,QAAQ,IAAI,EAAE;gDACvC,WAAU;0DAET,QAAQ,KAAK,CAAC,OAAO;;;;;;;;;;;sDAK1B,6LAAC;4CAAE,WAAU;sDACV,QAAQ,OAAO,CAAC,OAAO;;;;;;wCAIzB,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,mBACrC,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC7B,6LAAC;oDAEC,WAAU;;wDACX;wDACG;;mDAHG;;;;;;;;;;sDAUb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,CAAC,EAAE,OAAO,MAAM,EAAE,QAAQ,IAAI,EAAE;4CACvC,WAAU;;gDAET,EAAE;gDACF,sBACC,6LAAC,4NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;yEAEzB,6LAAC,8NAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;2BArF3B,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;;;AA+F9B;GAzJgB;;QACJ,yMAAA,CAAA,kBAAe;QACV,qKAAA,CAAA,YAAS;QAGU,iKAAA,CAAA,UAAM;;;KAL1B", "debugId": null}}, {"offset": {"line": 698, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/newwww/apps/frontend/src/components/sections/AchievementsSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useTranslations, useLocale } from 'next-intl';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport useSWR from 'swr';\nimport { format } from 'date-fns';\nimport { ar, enUS } from 'date-fns/locale';\nimport { ArrowRightIcon, ArrowLeftIcon, CalendarIcon } from '@heroicons/react/24/outline';\n\nexport function AchievementsSection() {\n  const t = useTranslations();\n  const locale = useLocale();\n  const isRTL = locale === 'ar';\n  \n  const { data: settingsData, error } = useSWR('/api/settings');\n  \n  if (error) {\n    return null;\n  }\n\n  const achievements = settingsData?.data?.settings?.achievements || [];\n\n  if (achievements.length === 0) {\n    return null;\n  }\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return format(date, 'MMMM yyyy', { \n      locale: locale === 'ar' ? ar : enUS \n    });\n  };\n\n  // Show only the latest 3 achievements\n  const latestAchievements = achievements\n    .sort((a: any, b: any) => new Date(b.date).getTime() - new Date(a.date).getTime())\n    .slice(0, 3);\n\n  return (\n    <section className=\"py-16 bg-gray-50 dark:bg-gray-800\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl\">\n            {t('home.achievements')}\n          </h2>\n          <p className=\"mt-4 text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto\">\n            {locale === 'ar' \n              ? 'أبرز الإنجازات والمشاريع التي تم تحقيقها لخدمة الشعب العراقي'\n              : 'Key achievements and projects accomplished to serve the Iraqi people'\n            }\n          </p>\n        </div>\n\n        {/* Achievements Timeline */}\n        <div className=\"relative\">\n          {/* Timeline line */}\n          <div className=\"absolute left-1/2 transform -translate-x-1/2 w-0.5 h-full bg-blue-200 dark:bg-blue-800 hidden lg:block\" />\n\n          <div className=\"space-y-12\">\n            {latestAchievements.map((achievement: any, index: number) => (\n              <div\n                key={achievement._id || index}\n                className={`relative flex items-center ${\n                  index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'\n                } flex-col lg:space-x-8 rtl:space-x-reverse`}\n              >\n                {/* Timeline dot */}\n                <div className=\"absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-600 rounded-full border-4 border-white dark:border-gray-800 hidden lg:block z-10\" />\n\n                {/* Content */}\n                <div className={`w-full lg:w-5/12 ${index % 2 === 0 ? '' : 'lg:text-right rtl:text-left'}`}>\n                  <div className=\"bg-white dark:bg-gray-900 rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-200\">\n                    {/* Date */}\n                    <div className=\"flex items-center text-blue-600 dark:text-blue-400 mb-3\">\n                      <CalendarIcon className=\"h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2\" />\n                      <span className=\"text-sm font-medium\">\n                        {formatDate(achievement.date)}\n                      </span>\n                    </div>\n\n                    {/* Title */}\n                    <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-3\">\n                      {achievement.title[locale]}\n                    </h3>\n\n                    {/* Description */}\n                    <p className=\"text-gray-600 dark:text-gray-300 mb-4 leading-relaxed\">\n                      {achievement.description[locale]}\n                    </p>\n\n                    {/* Image */}\n                    {achievement.image && (\n                      <div className=\"relative h-48 rounded-lg overflow-hidden mb-4\">\n                        <Image\n                          src={achievement.image}\n                          alt={achievement.title[locale]}\n                          fill\n                          className=\"object-cover\"\n                          sizes=\"(max-width: 768px) 100vw, 50vw\"\n                        />\n                      </div>\n                    )}\n                  </div>\n                </div>\n\n                {/* Spacer for the other side */}\n                <div className=\"hidden lg:block w-5/12\" />\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* View All Link */}\n        {achievements.length > 3 && (\n          <div className=\"text-center mt-12\">\n            <Link\n              href={`/${locale}/biography#achievements`}\n              className=\"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200\"\n            >\n              {t('home.viewAllAchievements')}\n              {isRTL ? (\n                <ArrowLeftIcon className=\"mr-2 h-5 w-5\" />\n              ) : (\n                <ArrowRightIcon className=\"ml-2 h-5 w-5\" />\n              )}\n            </Link>\n          </div>\n        )}\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;;;AARA;;;;;;;;AAUO,SAAS;;IACd,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;IACvB,MAAM,QAAQ,WAAW;IAEzB,MAAM,EAAE,MAAM,YAAY,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAM,AAAD,EAAE;IAE7C,IAAI,OAAO;QACT,OAAO;IACT;IAEA,MAAM,eAAe,cAAc,MAAM,UAAU,gBAAgB,EAAE;IAErE,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,OAAO;IACT;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,aAAa;YAC/B,QAAQ,WAAW,OAAO,8IAAA,CAAA,KAAE,GAAG,oJAAA,CAAA,OAAI;QACrC;IACF;IAEA,sCAAsC;IACtC,MAAM,qBAAqB,aACxB,IAAI,CAAC,CAAC,GAAQ,IAAW,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,IAC9E,KAAK,CAAC,GAAG;IAEZ,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,6LAAC;4BAAE,WAAU;sCACV,WAAW,OACR,iEACA;;;;;;;;;;;;8BAMR,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;;;;;sCAEf,6LAAC;4BAAI,WAAU;sCACZ,mBAAmB,GAAG,CAAC,CAAC,aAAkB,sBACzC,6LAAC;oCAEC,WAAW,CAAC,2BAA2B,EACrC,QAAQ,MAAM,IAAI,gBAAgB,sBACnC,0CAA0C,CAAC;;sDAG5C,6LAAC;4CAAI,WAAU;;;;;;sDAGf,6LAAC;4CAAI,WAAW,CAAC,iBAAiB,EAAE,QAAQ,MAAM,IAAI,KAAK,+BAA+B;sDACxF,cAAA,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,0NAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;0EACxB,6LAAC;gEAAK,WAAU;0EACb,WAAW,YAAY,IAAI;;;;;;;;;;;;kEAKhC,6LAAC;wDAAG,WAAU;kEACX,YAAY,KAAK,CAAC,OAAO;;;;;;kEAI5B,6LAAC;wDAAE,WAAU;kEACV,YAAY,WAAW,CAAC,OAAO;;;;;;oDAIjC,YAAY,KAAK,kBAChB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4DACJ,KAAK,YAAY,KAAK;4DACtB,KAAK,YAAY,KAAK,CAAC,OAAO;4DAC9B,IAAI;4DACJ,WAAU;4DACV,OAAM;;;;;;;;;;;;;;;;;;;;;;sDAQhB,6LAAC;4CAAI,WAAU;;;;;;;mCA7CV,YAAY,GAAG,IAAI;;;;;;;;;;;;;;;;gBAoD/B,aAAa,MAAM,GAAG,mBACrB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAM,CAAC,CAAC,EAAE,OAAO,uBAAuB,CAAC;wBACzC,WAAU;;4BAET,EAAE;4BACF,sBACC,6LAAC,4NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;qDAEzB,6LAAC,8NAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C;GA3HgB;;QACJ,yMAAA,CAAA,kBAAe;QACV,qKAAA,CAAA,YAAS;QAGc,iKAAA,CAAA,UAAM;;;KAL9B", "debugId": null}}]}