# 🚀 دليل النشر | Deployment Guide

## 📋 قائمة التحقق قبل النشر | Pre-Deployment Checklist

- [ ] تم تشغيل الموقع محلياً بنجاح
- [ ] تم إنشاء المستخدم الأول
- [ ] تم تخصيص المحتوى الأساسي
- [ ] تم اختبار جميع الوظائف
- [ ] تم إعداد متغيرات البيئة

## 🌐 نشر الواجهة الأمامية | Frontend Deployment

### Vercel (مجاني - موصى به)

1. **إعداد المستودع**:
```bash
git init
git add .
git commit -m "Initial commit"
git remote add origin YOUR_GITHUB_REPO_URL
git push -u origin main
```

2. **النشر على Vercel**:
   - اذهب إلى [vercel.com](https://vercel.com)
   - سجل دخول بحساب GitHub
   - اختر "Import Project"
   - اختر مستودع المشروع
   - اضبط الإعدادات:
     - **Framework Preset**: Next.js
     - **Root Directory**: `apps/frontend`
     - **Build Command**: `npm run build`
     - **Output Directory**: `.next`

3. **متغيرات البيئة**:
```
NEXT_PUBLIC_SITE_URL=https://your-domain.vercel.app
NEXT_PUBLIC_API_URL=https://your-backend-url.com
```

### Netlify (بديل)

1. **إعداد netlify.toml**:
```toml
[build]
  base = "apps/frontend"
  command = "npm run build"
  publish = ".next"

[[redirects]]
  from = "/api/*"
  to = "https://your-backend-url.com/api/:splat"
  status = 200
```

## 🔧 نشر الواجهة الخلفية | Backend Deployment

### Railway (مجاني - موصى به)

1. **إعداد railway.json**:
```json
{
  "$schema": "https://railway.app/railway.schema.json",
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "cd apps/backend && npm start",
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}
```

2. **النشر**:
   - اذهب إلى [railway.app](https://railway.app)
   - سجل دخول بحساب GitHub
   - اختر "Deploy from GitHub repo"
   - اختر المستودع

3. **متغيرات البيئة**:
```
NODE_ENV=production
PORT=5000
MONGODB_URI=mongodb+srv://username:<EMAIL>/iraqi_politician_db
JWT_SECRET=your_super_secret_jwt_key_change_in_production
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
CORS_ORIGINS=https://your-frontend-domain.vercel.app
```

### Render (بديل)

1. **إعداد render.yaml**:
```yaml
services:
  - type: web
    name: iraqi-politician-backend
    env: node
    buildCommand: cd apps/backend && npm install && npm run build
    startCommand: cd apps/backend && npm start
    envVars:
      - key: NODE_ENV
        value: production
```

### Heroku (بديل)

1. **إعداد Procfile**:
```
web: cd apps/backend && npm start
```

## 🗄️ إعداد قاعدة البيانات | Database Setup

### MongoDB Atlas (مجاني)

1. **إنشاء Cluster**:
   - اذهب إلى [mongodb.com/atlas](https://www.mongodb.com/atlas)
   - أنشئ حساب مجاني
   - أنشئ cluster جديد (M0 مجاني)

2. **إعداد الأمان**:
   - أنشئ database user
   - اضبط Network Access (0.0.0.0/0 للوصول من أي مكان)

3. **الحصول على Connection String**:
   - اذهب إلى "Connect" > "Connect your application"
   - انسخ connection string
   - استبدل `<password>` بكلمة المرور

## 📧 إعداد الإيميل | Email Setup

### Gmail SMTP

1. **تفعيل 2-Factor Authentication**
2. **إنشاء App Password**:
   - اذهب إلى Google Account settings
   - Security > 2-Step Verification > App passwords
   - أنشئ app password جديد

3. **متغيرات البيئة**:
```
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
```

## 🔐 الأمان في الإنتاج | Production Security

### متغيرات البيئة المطلوبة:

```bash
# Backend
NODE_ENV=production
JWT_SECRET=generate_strong_random_secret_here
MONGODB_URI=your_mongodb_atlas_connection_string
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
CORS_ORIGINS=https://your-frontend-domain.com

# Frontend
NEXT_PUBLIC_SITE_URL=https://your-domain.com
NEXT_PUBLIC_API_URL=https://your-backend-domain.com
```

### نصائح الأمان:

1. **غيّر JWT_SECRET**: استخدم مولد كلمات مرور قوية
2. **اضبط CORS**: حدد النطاقات المسموحة فقط
3. **استخدم HTTPS**: تأكد من تفعيل SSL
4. **غيّر كلمة مرور الأدمن**: بعد أول تسجيل دخول

## 🌍 إعداد النطاق | Domain Setup

### ربط نطاق مخصص:

1. **Vercel**:
   - اذهب إلى Project Settings > Domains
   - أضف النطاق المخصص
   - اضبط DNS records

2. **Cloudflare** (اختياري للحماية):
   - أضف الموقع إلى Cloudflare
   - اضبط DNS proxy
   - فعّل SSL/TLS

## 📊 المراقبة والتحليلات | Monitoring & Analytics

### Google Analytics:
```javascript
// في .env.local
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
```

### Sentry (مراقبة الأخطاء):
```bash
npm install @sentry/nextjs @sentry/node
```

## 🔄 التحديثات | Updates

### تحديث الموقع:
```bash
git add .
git commit -m "Update content"
git push origin main
# Vercel سيقوم بالنشر تلقائياً
```

### تحديث قاعدة البيانات:
```bash
# تشغيل migration scripts إذا لزم الأمر
cd apps/backend
npm run migrate
```

## 🆘 استكشاف الأخطاء | Troubleshooting

### مشاكل شائعة:

1. **Build Errors**:
   - تحقق من متغيرات البيئة
   - تأكد من صحة package.json

2. **Database Connection**:
   - تحقق من MONGODB_URI
   - تأكد من Network Access في Atlas

3. **CORS Errors**:
   - تحقق من CORS_ORIGINS
   - تأكد من تطابق النطاقات

4. **Email Not Working**:
   - تحقق من App Password
   - تأكد من تفعيل 2FA

## 📞 الدعم | Support

للحصول على المساعدة:
- راجع logs في منصة النشر
- تحقق من متغيرات البيئة
- تأكد من صحة connection strings

---

**نشر موفق! 🚀**
**Happy Deployment! 🚀**
