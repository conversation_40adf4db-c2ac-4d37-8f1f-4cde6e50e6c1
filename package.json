{"name": "iraqi-politician-website", "version": "1.0.0", "description": "Full-stack website for Iraqi politician with admin dashboard", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd apps/frontend && npm run dev", "dev:backend": "cd apps/backend && npm run dev", "dev:admin": "cd apps/admin && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd apps/frontend && npm run build", "build:backend": "cd apps/backend && npm run build", "build:admin": "cd apps/admin && npm run build", "start": "concurrently \"npm run start:frontend\" \"npm run start:backend\"", "start:frontend": "cd apps/frontend && npm run start", "start:backend": "cd apps/backend && npm run start", "start:admin": "cd apps/admin && npm run start", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd apps/frontend && npm run lint", "lint:backend": "cd apps/backend && npm run lint", "lint:admin": "cd apps/admin && npm run lint", "setup": "npm run install:all && npm run create-admin", "install:all": "npm install && cd apps/frontend && npm install && cd ../backend && npm install && cd ../admin && npm install", "create-admin": "cd apps/backend && npm run create-admin", "clean": "rm -rf node_modules apps/*/node_modules apps/*/.next apps/*/dist"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "author": "Iraqi Politician Website Team", "license": "MIT", "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "axios": "^1.9.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.513.0", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "react-hook-form": "^7.57.0", "react-hot-toast": "^2.5.2", "swr": "^2.3.3", "tailwind-merge": "^3.3.0", "zod": "^3.25.56"}}