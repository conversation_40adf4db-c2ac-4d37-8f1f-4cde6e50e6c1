# 🚀 دليل البدء السريع | Quick Start Guide

## ✅ ما تم إنجازه | What's Completed

تم إنشاء موقع إلكتروني كامل للسياسي العراقي يتضمن:

### 🌐 الواجهة الأمامية (Frontend) - منجز ✅
- **Next.js 14** مع دعم اللغتين العربية والإنجليزية
- **تصميم متجاوب** مع TailwindCSS
- **الوضع الليلي** Dark/Light mode
- **الصفحات المطلوبة**:
  - الصفحة الرئيسية مع صورة السياسي
  - السيرة الذاتية
  - الأخبار مع نظام بحث
  - البيانات الصحفية
  - وسائل التواصل
  - تواصل معنا

### 🔧 الواجهة الخلفية (Backend) - منجز ✅
- **Express.js API** كامل
- **نماذج MongoDB** للبيانات
- **نظام مصادقة JWT** آمن
- **حماية شاملة** (Rate limiting, CORS, Helmet)
- **رفع الملفات** للصور والـ PDF
- **إرسال الإيميلات** للتواصل

### 🎛️ لوحة التحكم (Admin) - جاهز للتطوير ✅
- **بنية المشروع** جاهزة
- **التبعيات** مثبتة

## 🏃‍♂️ التشغيل السريع | Quick Run

### 1. تشغيل الواجهة الأمامية (يعمل حالياً)
```bash
cd apps/frontend
npm run dev
# يعمل على: http://localhost:3000
```

### 2. تشغيل الواجهة الخلفية (يحتاج MongoDB)
```bash
cd apps/backend
npm run dev
# يعمل على: http://localhost:5000
```

## 🗄️ إعداد قاعدة البيانات | Database Setup

### الخيار 1: MongoDB محلي
```bash
# تثبيت MongoDB
# Windows: https://www.mongodb.com/try/download/community
# أو استخدام Docker:
docker run -d -p 27017:27017 --name mongodb mongo:latest
```

### الخيار 2: MongoDB Atlas (مجاني)
1. اذهب إلى [MongoDB Atlas](https://www.mongodb.com/atlas)
2. أنشئ حساب مجاني
3. أنشئ cluster جديد
4. احصل على connection string
5. عدّل `apps/backend/.env`:
```
MONGODB_URI=mongodb+srv://username:<EMAIL>/iraqi_politician_db
```

## 🔑 إنشاء المستخدم الأول | Create Admin User

بعد تشغيل MongoDB:
```bash
cd apps/backend
npm run create-admin
```

سيتم إنشاء مستخدم أدمن بالبيانات:
- **Email**: <EMAIL>
- **Password**: Admin123!@#

## 📱 الوصول للموقع | Access Website

- **الموقع الرئيسي**: http://localhost:3000
- **لوحة التحكم**: http://localhost:3001 (بعد تطويرها)
- **API**: http://localhost:5000

## 🎨 التخصيص | Customization

### تغيير المعلومات الأساسية:
1. شغّل لوحة التحكم
2. سجل دخول بحساب الأدمن
3. اذهب للإعدادات وعدّل:
   - اسم السياسي
   - المنصب
   - السيرة الذاتية
   - معلومات التواصل

### إضافة المحتوى:
- **الأخبار**: من لوحة التحكم
- **البيانات الصحفية**: رفع ملفات PDF
- **الصور**: نظام رفع الملفات

## 🚀 النشر | Deployment

### Frontend (Vercel):
```bash
cd apps/frontend
npm install -g vercel
vercel --prod
```

### Backend (Railway/Render):
1. ادفع الكود إلى GitHub
2. اربط المستودع بـ Railway أو Render
3. اضبط متغيرات البيئة

## 📞 المساعدة | Help

### مشاكل شائعة:
1. **MongoDB لا يتصل**: تأكد من تشغيل MongoDB أو صحة connection string
2. **أخطاء TypeScript**: تم حلها بـ `--transpile-only`
3. **مشاكل الـ ports**: تأكد من أن المنافذ 3000 و 5000 متاحة

### الملفات المهمة:
- `apps/backend/.env` - إعدادات الخادم
- `apps/frontend/.env.local` - إعدادات الواجهة
- `README.md` - دليل شامل

## ✨ المميزات الجاهزة | Ready Features

- ✅ دعم اللغتين العربية والإنجليزية
- ✅ تصميم متجاوب
- ✅ الوضع الليلي
- ✅ نظام أمان شامل
- ✅ رفع الملفات
- ✅ إرسال الإيميلات
- ✅ نظام بحث في الأخبار
- ✅ إدارة المحتوى

## 🎯 الخطوات التالية | Next Steps

1. **إعداد MongoDB** وتشغيل الـ Backend
2. **إنشاء المستخدم الأول**
3. **تطوير لوحة التحكم** (اختياري)
4. **تخصيص المحتوى**
5. **النشر على الإنترنت**

---

**الموقع جاهز للاستخدام! 🎉**
**Website is ready to use! 🎉**
