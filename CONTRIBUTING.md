# 🤝 دليل المساهمة | Contributing Guide

نرحب بمساهماتكم في تطوير موقع السياسي العراقي! هذا الدليل سيساعدكم على البدء.

We welcome your contributions to the Iraqi Politician Website! This guide will help you get started.

## 🚀 البدء السريع | Quick Start

### 1. استنساخ المشروع | Fork & Clone
```bash
# Fork the repository on GitHub
git clone https://github.com/YOUR_USERNAME/iraqi-politician-website.git
cd iraqi-politician-website
```

### 2. إعداد البيئة | Environment Setup
```bash
# تثبيت التبعيات | Install dependencies
npm run install:all

# إعداد متغيرات البيئة | Setup environment variables
cp apps/backend/.env.example apps/backend/.env
cp apps/frontend/.env.example apps/frontend/.env.local

# تشغيل التطبيق | Run the application
npm run dev
```

## 📋 أنواع المساهمات | Types of Contributions

### 🐛 الإبلاغ عن الأخطاء | Bug Reports
- استخدم GitHub Issues
- اتبع قالب الإبلاغ عن الأخطاء
- قدم معلومات مفصلة عن المشكلة

### ✨ طلب مميزات جديدة | Feature Requests
- افتح GitHub Issue
- اشرح الميزة المطلوبة بالتفصيل
- اذكر الفائدة المتوقعة

### 🔧 إصلاح الأخطاء | Bug Fixes
- ابحث عن Issues مع label "bug"
- أنشئ branch جديد للإصلاح
- اكتب tests للتأكد من الإصلاح

### 📚 تحسين التوثيق | Documentation
- تحسين README.md
- إضافة تعليقات في الكود
- كتابة أدلة إضافية

## 🛠️ عملية التطوير | Development Process

### 1. إنشاء Branch جديد | Create New Branch
```bash
git checkout -b feature/your-feature-name
# أو | or
git checkout -b fix/bug-description
```

### 2. معايير الكود | Code Standards

#### Frontend (Next.js/React)
- استخدم TypeScript
- اتبع ESLint rules
- استخدم TailwindCSS للتصميم
- اكتب components قابلة لإعادة الاستخدام

#### Backend (Express.js)
- استخدم TypeScript
- اتبع RESTful API principles
- اكتب validation للمدخلات
- استخدم proper error handling

#### عام | General
- اكتب تعليقات واضحة
- استخدم أسماء متغيرات وصفية
- اتبع consistent formatting

### 3. الاختبار | Testing
```bash
# تشغيل الاختبارات | Run tests
npm run test

# فحص الكود | Lint code
npm run lint

# فحص الأنواع | Type check
npm run type-check
```

### 4. Commit Messages
استخدم conventional commits:
```bash
feat: add user authentication
fix: resolve database connection issue
docs: update installation guide
style: format code with prettier
refactor: improve error handling
test: add unit tests for auth
```

## 🌐 دعم اللغات | Language Support

### إضافة ترجمات جديدة | Adding New Translations
1. أضف الترجمات في `apps/frontend/src/i18n/messages/`
2. تأكد من دعم RTL للغات العربية
3. اختبر التصميم مع النصوص الطويلة

### تحسين الترجمات الموجودة | Improving Existing Translations
- راجع الملفات في `messages/ar.json` و `messages/en.json`
- تأكد من دقة الترجمة والسياق
- احرص على الاتساق في المصطلحات

## 🔒 الأمان | Security

### الإبلاغ عن مشاكل أمنية | Reporting Security Issues
- **لا تفتح** GitHub Issue للمشاكل الأمنية
- راسلنا مباشرة على البريد الإلكتروني
- سنرد خلال 48 ساعة

### أفضل الممارسات | Best Practices
- لا تضع secrets في الكود
- استخدم environment variables
- validate جميع المدخلات
- استخدم HTTPS في الإنتاج

## 📝 Pull Request Process

### 1. قبل إرسال PR | Before Submitting PR
- [ ] تأكد من عمل الكود محلياً
- [ ] اكتب/حدث الاختبارات
- [ ] تأكد من نجاح جميع الاختبارات
- [ ] حدث التوثيق إذا لزم الأمر
- [ ] تأكد من عدم وجود merge conflicts

### 2. وصف PR | PR Description
```markdown
## الوصف | Description
وصف مختصر للتغييرات

## نوع التغيير | Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## الاختبار | Testing
- [ ] تم اختبار الكود محلياً
- [ ] تم إضافة اختبارات جديدة
- [ ] جميع الاختبارات تنجح

## Screenshots (إذا كان مناسباً)
```

### 3. مراجعة الكود | Code Review
- سيتم مراجعة PR من قبل المشرفين
- قد نطلب تعديلات
- تفاعل مع التعليقات بإيجابية

## 🎯 أولويات التطوير | Development Priorities

### عالية الأولوية | High Priority
- إصلاح الأخطاء الأمنية
- تحسين الأداء
- إصلاح bugs حرجة

### متوسطة الأولوية | Medium Priority
- مميزات جديدة
- تحسين UX/UI
- تحسين التوثيق

### منخفضة الأولوية | Low Priority
- تحسينات تجميلية
- refactoring غير ضروري

## 🏷️ Labels في GitHub

- `bug` - مشاكل تحتاج إصلاح
- `enhancement` - مميزات جديدة
- `documentation` - تحسينات في التوثيق
- `good first issue` - مناسب للمبتدئين
- `help wanted` - نحتاج مساعدة
- `priority: high` - أولوية عالية

## 📞 التواصل | Communication

### قنوات التواصل | Communication Channels
- GitHub Issues للمشاكل والمميزات
- GitHub Discussions للنقاشات العامة
- البريد الإلكتروني للمشاكل الأمنية

### آداب التواصل | Communication Etiquette
- كن محترماً ومهذباً
- استخدم لغة واضحة ومفهومة
- قدم معلومات كافية
- تفاعل بإيجابية مع التعليقات

## 🎉 الاعتراف بالمساهمين | Recognition

- سيتم إضافة اسمك في قائمة المساهمين
- المساهمات الكبيرة ستحصل على شكر خاص
- نقدر جميع أنواع المساهمات

## 📚 موارد مفيدة | Helpful Resources

### التوثيق | Documentation
- [Next.js Documentation](https://nextjs.org/docs)
- [Express.js Guide](https://expressjs.com/en/guide/routing.html)
- [MongoDB Manual](https://docs.mongodb.com/)
- [TailwindCSS Docs](https://tailwindcss.com/docs)

### أدوات التطوير | Development Tools
- [VS Code](https://code.visualstudio.com/)
- [Postman](https://www.postman.com/) لاختبار APIs
- [MongoDB Compass](https://www.mongodb.com/products/compass)

---

**شكراً لمساهمتكم! 🙏**
**Thank you for your contributions! 🙏**
