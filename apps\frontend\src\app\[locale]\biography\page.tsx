import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { BiographyContent } from '@/components/pages/BiographyContent';

interface BiographyPageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: BiographyPageProps): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'biography' });
  
  return {
    title: t('title'),
    description: locale === 'ar' 
      ? 'السيرة الذاتية للسياسي العراقي - تعرف على مسيرته السياسية وإنجازاته'
      : 'Biography of the Iraqi politician - Learn about his political career and achievements',
    openGraph: {
      title: t('title'),
      description: locale === 'ar' 
        ? 'السيرة الذاتية للسياسي العراقي - تعرف على مسيرته السياسية وإنجازاته'
        : 'Biography of the Iraqi politician - Learn about his political career and achievements',
      type: 'profile',
    },
  };
}

export default function BiographyPage({ params: { locale } }: BiographyPageProps) {
  return <BiographyContent />;
}
